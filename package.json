{"name": "joymew-customer", "version": "0.1.0", "private": true, "scripts": {"lint": "vue-cli-service lint", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "build:stageonline": "vue-cli-service build --mode stageonline", "build:company": "vue-cli-service build --mode company", "dev": "vue-cli-service serve", "local": "vue-cli-service serve --mode local"}, "dependencies": {"axios": "^0.20.0", "clipboard": "^2.0.6", "core-js": "^3.6.5", "countup.js": "^2.0.7", "dayjs": "^1.11.10", "element-ui": "^2.8.2", "lodash": "^4.17.21", "qiniu-js": "^3.4.0", "qs": "^6.9.4", "serve": "^11.3.2", "vue": "^2.6.11", "vue-baidu-map": "^0.21.22", "vue-clipboard2": "^0.3.1", "vue-core-video-player": "^0.2.0", "vue-cropper": "^0.3.6", "vue-router": "^3.4.8", "vue-smooth-dnd": "^0.8.1", "vuedraggable": "^2.24.3", "vuescroll": "^4.9.0-beta.11", "vuex": "^3.4.0", "vuex-persistedstate": "^4.0.0-beta.1"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-airbnb": "^5.0.2", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "node-sass": "^4.10.0", "sass-loader": "^7.0.3", "style-resources-loader": "^1.3.2", "vue-cli-plugin-style-resources-loader": "~0.1.4", "vue-template-compiler": "^2.6.11"}}