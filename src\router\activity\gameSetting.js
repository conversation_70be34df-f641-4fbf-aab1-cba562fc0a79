export default [
  {
    path: 'signWeddingBook',
    name: 'signWeddingBook',
    component: () => import('@/views/activityViews/gamesSetting/signWeddingBook'),
    meta: {
      index: '0',
      title: '签婚书',
      icon: 'el-icon-joymew-toupiao',
      parentIndex: '2',
    },
  },
  {
    path: 'giftAllFree',
    name: 'giftAllFree',
    component: () => import('@/views/activityViews/gamesSetting/giftAllFree'),
    meta: {
      index: '1',
      title: '礼物包场',
      icon: 'el-icon-joymew-liwu',
      parentIndex: '2',
    },
  },
  {
    path: 'shineAnywhere',
    name: 'shineAnywhere',
    component: () => import('@/views/activityViews/gamesSetting/shineAnywhere'),
    meta: {
      index: '17',
      title: '签到红包',
      icon: 'el-icon-joymew-qiandao',
      parentIndex: '2',
    },
  },
  {
    path: 'hby',
    name: 'hby',
    component: () => import('@/views/activityViews/gamesSetting/redPackageRain'),
    meta: {
      index: '2',
      title: '红包雨',
      icon: 'el-icon-joymew-hongbao',
      parentIndex: '2',
    },
  },
  {
    path: 'swing',
    name: 'swing',
    component: () => import('@/views/activityViews/gamesSetting/swing'),
    meta: {
      index: '3',
      title: '摇一摇',
      icon: 'el-icon-joymew-shake',
      parentIndex: '2',
    },
  },
  {
    path: 'countMoney',
    name: 'countMoney',
    component: () => import('@/views/activityViews/gamesSetting/countMoney'),
    meta: {
      index: '4',
      title: '划一划',
      icon: 'el-icon-joymew-dianji01',
      parentIndex: '2',
    },
  },
  {
    path: 'awardSetting',
    name: 'awardSetting',
    component: () => import('@/views/activityViews/gamesSetting/awardSetting'),
    meta: {
      index: '5',
      title: '奖项设置',
      icon: 'el-icon-joymew-zidingyi1',
      parentIndex: '2',
    },
  },
  {
    path: 'signinLottery',
    name: 'signinLottery',
    component: () => import('@/views/activityViews/gamesSetting/signinLottery'),
    meta: {
      index: '6',
      title: '签到抽奖',
      icon: 'el-icon-joymew-choujiang',
      parentIndex: '2',
    },
  },
  {
    path: 'customUser',
    name: 'customUser',
    component: () => import('@/views/activityViews/gamesSetting/customUser'),
    meta: {
      index: '7',
      title: '自定义用户',
      icon: 'el-icon-joymew-zidingyi',
      parentIndex: '2',
    },
  },
  {
    path: 'listLottery',
    name: 'listLottery',
    component: () => import('@/views/activityViews/gamesSetting/listLottery/index'),
    meta: {
      index: '8',
      title: '名单抽奖',
      icon: 'el-icon-joymew-dianmingdan',
      parentIndex: '2',
    },
  },
  {
    path: 'voteNew',
    name: 'voteNew',
    component: () => import('@/views/activityViews/activityInfo/voteNew'),
    meta: {
      index: '9',
      title: '投票',
      icon: 'el-icon-joymew-toupiao',
      parentIndex: '2',
    },
  },
  {
    path: 'guessStar',
    name: 'guessStar',
    component: () => import('@/views/activityViews/gamesSetting/guessStar/index'),
    meta: {
      index: '10',
      title: '猜明星设置',
      icon: 'el-icon-joymew-mingxing',
      parentIndex: '2',
    },
  },
  {
    path: 'guessBrand',
    name: 'guessBrand',
    component: () => import('@/views/activityViews/gamesSetting/guessBrand/index'),
    meta: {
      index: '10',
      title: '品牌达人设置',
      icon: 'el-icon-joymew-pinpai',
      parentIndex: '2',
    },
  },
  {
    path: 'dragonBoatTeamShake',
    name: 'dragonBoatTeamShake',
    component: () => import('@/views/activityViews/gamesSetting/dragonBoatTeamShake'),
    meta: {
      index: '11',
      title: '赛龙舟设置',
      icon: 'el-icon-joymew-longzhou',
      parentIndex: '2',
    },
  },
  {
    path: 'giveMark',
    name: 'giveMark',
    component: () => import('@/views/activityViews/activityInfo/giveMark'),
    meta: {
      index: '16',
      title: '评分',
      icon: 'el-icon-joymew-pingfen-xing',
      parentIndex: '2',
    },
  },
  {
    path: 'photoLotteryw',
    name: 'photoLottery',
    component: () => import('@/views/activityViews/gamesSetting/photoLottery'),
    meta: {
      index: '12',
      title: '照片抽奖',
      icon: 'el-icon-joymew-benrenzhaopian',
      parentIndex: '2',
    },
  },
  {
    path: 'xydbCustom',
    name: 'xydbCustom',
    component: () => import('@/views/activityViews/gamesSetting/xydbCustom.vue'),
    meta: {
      index: '13',
      title: '幸运夺宝自定义',
      icon: 'el-icon-joymew-duobao1',
      parentIndex: '2',
    },
  },
  {
    path: 'kbCustom',
    name: 'kbCustom',
    component: () => import('@/views/activityViews/gamesSetting/kbCustom.vue'),
    meta: {
      index: '14',
      title: '开宝内定',
      icon: 'el-icon-joymew-shouye1',
      parentIndex: '2',
    },
  },
  {
    path: 'cardDrawCustom',
    name: 'cardDrawCustom',
    component: () => import('@/views/activityViews/gamesSetting/cardDrawCustom.vue'),
    meta: {
      index: '15',
      title: '卡牌抽奖设置',
      icon: 'el-icon-joymew-kapai',
      parentIndex: '2',
    },
  },
  {
    path: 'wheelLottery',
    name: 'wheelLottery',
    component: () => import('@/views/activityViews/gamesSetting/wheelLottery.vue'),
    meta: {
      index: '20',
      title: '转盘抽奖',
      icon: 'el-icon-bangzhu',
      parentIndex: '2',
    },
  },
  {
    path: 'guessCommonSetting',
    name: 'guessCommonSetting',
    component: () => import('@/views/activityViews/gamesSetting/guessCommonSetting'),
    meta: {
      index: '16',
      title: '猜电影设置',
      icon: 'el-icon-joymew-cainixihuanfuben',
      parentIndex: '2',
    },
  },
  {
    path: 'guessSpeechSetting',
    name: 'guessSpeechSetting',
    component: () => import('@/views/activityViews/gamesSetting/guessSpeechSetting/index'),
    meta: {
      index: '16',
      title: '台词模仿秀',
      icon: 'el-icon-joymew-cainixihuanfuben',
      parentIndex: '2',
    },
  },
  {
    path: 'happyQA',
    name: 'happyQA',
    component: () => import('@/views/activityViews/gamesSetting/happyQA'),
    meta: {
      index: '18',
      title: '开心答题设置',
      icon: 'el-icon-joymew-yijituijian',
      parentIndex: '2',
    },
  },
  {
    path: 'mahjongSetting',
    name: 'mahjongSetting',
    component: () => import('@/views/activityViews/gamesSetting/mahjongSetting'),
    meta: {
      index: '19',
      title: '雀神大赛设置',
      icon: 'el-icon-joymew-majiangzhuo',
      parentIndex: '2',
    },
  },
  {
    path: 'gestureRiddle',
    name: 'gestureRiddle',
    component: () => import('@/views/activityViews/gamesSetting/gestureRiddle/index'),
    meta: {
      index: '21',
      title: '你划我猜',
      icon: 'el-icon-joymew-gesture-riddle',
      parentIndex: '2',
    },
  },
  {
    path: 'shoutHb',
    name: 'shoutHb',
    component: () => import('@/views/activityViews/gamesSetting/shoutHb/index'),
    meta: {
      index: '21',
      title: '喊红包',
      icon: 'el-icon-joymew-shishihanhua',
      parentIndex: '2',
    },
  },
];
