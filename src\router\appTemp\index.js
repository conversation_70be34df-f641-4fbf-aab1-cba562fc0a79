export default [
  {
    path: '/appTemp',
    component: () => import('@/layout/appTemp/index'),
    children: [
      {
        path: 'themeSetting',
        name: 'ThemeSetting',
        component: () => import('@/views/activityViews/activityInfo/themeSetting'),
        meta: {
          appTempIndex: '1',
          title: '主题设置',
          icon: 'el-icon-joymew-shouye1',
          hidden: true,
        },
      },
      {
        path: 'activityEdit',
        name: 'ActivityEdit',
        component: () => import('@/views/activityViews/activityInfo/activityEdit/index'),
        meta: {
          appTempIndex: '2',
          title: '信息修改',
          icon: 'el-icon-joymew-bianji',
          hidden: true,
        },
      },
      {
        path: 'companyLogo',
        name: 'CompanyLogo',
        component: () => import('@/views/activityViews/activityInfo/companyLogo'),
        meta: {
          appTempIndex: '3',
          title: 'LOGO设置',
          icon: 'el-icon-joymew-shezhi',
          hidden: true,
        },
      },
      {
        path: 'customWish',
        name: 'CustomWish',
        component: () => import('@/views/activityViews/activityInfo/customWish'),
        meta: {
          appTempIndex: '4',
          title: '自定义签到语',
          icon: 'el-icon-joymew-zidingyi',
          hidden: true,
        },
      },
      {
        path: 'getPhone',
        name: 'GetPhone',
        component: () => import('@/views/activityViews/activityInfo/getPhone'),
        meta: {
          appTempIndex: '5',
          title: '获取手机号',
          icon: 'el-icon-joymew-shoujihao',
          hidden: true,
        },
      },
      {
        path: 'launchCeremony',
        name: 'LaunchCeremony',
        component: () => import('@/views/activityViews/activityInfo/launchCeremony'),
        meta: {
          appTempIndex: '6',
          title: '启动仪式',
          icon: 'el-icon-joymew-4wed_arch',
          hidden: true,
        },
      },
      {
        path: 'seat',
        name: 'Seat',
        component: () => import('@/views/activityViews/activityInfo/seat'),
        meta: {
          appTempIndex: '7',
          title: '席位表',
          icon: 'el-icon-joymew-24gl-table',
          hidden: true,
        },
      },
      {
        path: 'locationLimit',
        name: 'LocationLimit',
        component: () => import('@/views/activityViews/activityInfo/locationLimit'),
        meta: {
          appTempIndex: '8',
          title: '地理位置限制',
          icon: 'el-icon-joymew-diliweizhi',
          hidden: true,
        },
      },
      {
        path: 'messageVerify',
        name: 'MessageVerify',
        component: () => import('@/views/activityViews/activityInfo/messageVerify'),
        meta: {
          appTempIndex: '9',
          title: '消息审核',
          icon: 'el-icon-joymew-shenhe',
          hidden: true,
        },
      },
      {
        path: 'signWhiteList',
        name: 'SignWhiteList',
        component: () => import('@/views/activityViews/activityInfo/signWhiteList'),
        meta: {
          appTempIndex: '10',
          title: '签到白名单',
          icon: 'el-icon-joymew-baimingdan',
          hidden: true,
        },
      },
      {
        path: 'shineAnywhere',
        name: 'ShineAnywhere',
        component: () => import('@/views/activityViews/gamesSetting/shineAnywhere'),
        meta: {
          appTempIndex: '11',
          title: '签到红包',
          icon: 'el-icon-joymew-qiandao',
          hidden: true,
        },
      },
      {
        path: 'hby',
        name: 'Hby',
        component: () => import('@/views/activityViews/gamesSetting/redPackageRain'),
        meta: {
          appTempIndex: '12',
          title: '红包雨',
          icon: 'el-icon-joymew-hongbao',
          hidden: true,
        },
      },
      {
        path: 'swing',
        name: 'Swing',
        component: () => import('@/views/activityViews/gamesSetting/swing'),
        meta: {
          appTempIndex: '13',
          title: '摇一摇',
          icon: 'el-icon-joymew-shake',
          hidden: true,
        },
      },
      {
        path: 'countMoney',
        name: 'CountMoney',
        component: () => import('@/views/activityViews/gamesSetting/countMoney'),
        meta: {
          appTempIndex: '14',
          title: '划一划',
          icon: 'el-icon-joymew-dianji01',
          hidden: true,
        },
      },
      {
        path: 'awardSetting',
        name: 'AwardSetting',
        component: () => import('@/views/activityViews/gamesSetting/awardSetting'),
        meta: {
          appTempIndex: '15',
          title: '奖项设置',
          icon: 'el-icon-joymew-zidingyi1',
          hidden: true,
        },
      },
      {
        path: 'signinLottery',
        name: 'SigninLottery',
        component: () => import('@/views/activityViews/gamesSetting/signinLottery'),
        meta: {
          appTempIndex: '16',
          title: '签到抽奖',
          icon: 'el-icon-joymew-choujiang',
          hidden: true,
        },
      },
      {
        path: 'customUser',
        name: 'CustomUser',
        component: () => import('@/views/activityViews/gamesSetting/customUser'),
        meta: {
          appTempIndex: '17',
          title: '自定义用户',
          icon: 'el-icon-joymew-zidingyi',
          hidden: true,
        },
      },
      {
        path: 'listLottery',
        name: 'ListLottery',
        component: () => import('@/views/activityViews/gamesSetting/listLottery/index'),
        meta: {
          appTempIndex: '18',
          title: '名单抽奖',
          icon: 'el-icon-joymew-dianmingdan',
          hidden: true,
        },
      },
      {
        path: 'voteNew',
        name: 'VoteNew',
        component: () => import('@/views/activityViews/activityInfo/voteNew'),
        meta: {
          appTempIndex: '19',
          title: '投票',
          icon: 'el-icon-joymew-toupiao',
          hidden: true,
        },
      },
      {
        path: 'guessStarSetting',
        name: 'GuessStarSetting',
        component: () => import('@/views/activityViews/gamesSetting/guessStarSetting'),
        meta: {
          appTempIndex: '20',
          title: '猜明星设置',
          icon: 'el-icon-joymew-mingxing',
          hidden: true,
        },
      },
      {
        path: 'dragonBoatTeamShake',
        name: 'DragonBoatTeamShake',
        component: () => import('@/views/activityViews/gamesSetting/dragonBoatTeamShake'),
        meta: {
          appTempIndex: '21',
          title: '赛龙舟设置',
          icon: 'el-icon-joymew-longzhou',
          hidden: true,
        },
      },
      {
        path: 'giveMark',
        name: 'GiveMark',
        component: () => import('@/views/activityViews/activityInfo/giveMark'),
        meta: {
          appTempIndex: '22',
          title: '评分',
          icon: 'el-icon-joymew-pingfen-xing',
          hidden: true,
        },
      },
      {
        path: 'photoLotteryw',
        name: 'PhotoLottery',
        component: () => import('@/views/activityViews/gamesSetting/photoLottery'),
        meta: {
          appTempIndex: '23',
          title: '照片抽奖',
          icon: 'el-icon-joymew-benrenzhaopian',
          hidden: true,
        },
      },
      {
        path: 'xydbCustom',
        name: 'XydbCustom',
        component: () => import('@/views/activityViews/gamesSetting/xydbCustom.vue'),
        meta: {
          appTempIndex: '24',
          title: '幸运夺宝自定义',
          icon: 'el-icon-joymew-duobao1',
          hidden: true,
        },
      },
      {
        path: 'kbCustom',
        name: 'KbCustom',
        component: () => import('@/views/activityViews/gamesSetting/kbCustom.vue'),
        meta: {
          appTempIndex: '25',
          title: '开宝内定',
          icon: 'el-icon-joymew-shouye1',
          hidden: true,
        },
      },
      {
        path: 'cardDrawCustom',
        name: 'CardDrawCustom',
        component: () => import('@/views/activityViews/gamesSetting/cardDrawCustom.vue'),
        meta: {
          appTempIndex: '26',
          title: '卡牌抽奖设置',
          icon: 'el-icon-joymew-kapai',
          hidden: true,
        },
      },
      {
        path: 'wheelLottery',
        name: 'WheelLottery',
        component: () => import('@/views/activityViews/gamesSetting/wheelLottery.vue'),
        meta: {
          appTempIndex: '27',
          title: '转盘抽奖',
          icon: 'el-icon-bangzhu',
          hidden: true,
        },
      },
      {
        path: 'guessCommonSetting',
        name: 'GuessCommonSetting',
        component: () => import('@/views/activityViews/gamesSetting/guessCommonSetting'),
        meta: {
          appTempIndex: '28',
          title: '猜电影设置',
          icon: 'el-icon-joymew-cainixihuanfuben',
          hidden: true,
        },
      },
      {
        path: 'happyQA',
        name: 'GappyQA',
        component: () => import('@/views/activityViews/gamesSetting/happyQA'),
        meta: {
          appTempIndex: '29',
          title: '开心答题设置',
          icon: 'el-icon-joymew-yijituijian',
          hidden: true,
        },
      },
      {
        path: 'mahjongSetting',
        name: 'MahjongSetting',
        component: () => import('@/views/activityViews/gamesSetting/mahjongSetting'),
        meta: {
          appTempIndex: '30',
          title: '雀神大赛设置',
          icon: 'el-icon-joymew-majiangzhuo',
          hidden: true,
        },
      },
      {
        path: 'gestureRiddle',
        name: 'GestureRiddle',
        component: () => import('@/views/activityViews/gamesSetting/gestureRiddle/index'),
        meta: {
          appTempIndex: '31',
          title: '你划我猜',
          icon: 'el-icon-joymew-gesture-riddle',
          hidden: true,
        },
      },
      {
        path: 'signIn',
        name: 'SignIn',
        component: () => import('@/views/activityViews/activityData/signIn'),
        meta: {
          appTempIndex: '32',
          title: '签到嘉宾',
          icon: 'el-icon-joymew-yonghuguanli_huaban',
          hidden: true,
        },
      },
      {
        path: 'winningRecord',
        name: 'WinningRecord',
        component: () => import('@/views/activityViews/activityData/winningRecord'),
        meta: {
          appTempIndex: '33',
          title: '中奖记录',
          icon: 'el-icon-joymew-jilu',
          hidden: true,
        },
      },
      {
        path: 'guessSpeechSetting',
        name: 'GuessSpeechSetting',
        component: () => import('@/views/activityViews/gamesSetting/guessSpeechSetting/index'),
        meta: {
          appTempIndex: '34',
          title: '台词模仿秀',
          icon: 'el-icon-joymew-cainixihuanfuben',
          hidden: true,
        },
      },
      {
        path: 'guessBrand',
        name: 'GuessBrand',
        component: () => import('@/views/activityViews/gamesSetting/guessBrand/index'),
        meta: {
          appTempIndex: '35',
          title: '品牌达人设置',
          icon: 'el-icon-joymew-pinpai',
          hidden: true,
        },
      },
      {
        path: 'shoutHb',
        name: 'ShoutHb',
        component: () => import('@/views/activityViews/gamesSetting/shoutHb/index'),
        meta: {
          appTempIndex: '36',
          title: '喊红包设置',
          icon: 'el-icon-joymew-shishihanhua',
          hidden: true,
        },
      },
      {
        path: 'curtainCall',
        name: 'CurtainCall',
        component: () => import('@/views/activityViews/activityInfo/curtainCall'),
        meta: {
          appTempIndex: '37',
          title: '谢幕设置',
          icon: 'el-icon-joymew-cunchufangshijieshu',
          hidden: true,
        },
      },
    ],
  },
];
