import store from '@/store/index';
import request from '@/utils/request';

/**
 * 获取猜台词数据
 */
export const reqGetGuessSpeech = () => request.get(`https://ustatic.joymew.com/joymewServer/guessSpeech.json?time=${Date.now()}`);

/**
 * 获取猜台词配置
 */
export const reqGetGuessSpeechConfig = () => request.get(`/activityMay2023/getRule?ruleKey=${store.state.liveId}-guessSpeech`);

/**
 * 保存猜台词配置
 */

export const saveGuessSpeechConfig = (valueStr) =>
  request.post('/activityMay2023/saveRulo', {
    ruleKey: `${store.state.liveId}-guessSpeech`,
    ruleValue: valueStr,
  });

/**
 * 喊红包初始化
 * @returns {Promise<String>}
 */
export async function reqShouHbData() {
  return request.post('/sheZhi/findInfo', { splid: store.state.liveId }).then((res) => res.data.han_hb_json);
}

/**
 * 喊红包更新
 * @param {String} danmu 弹幕 格式xxx_xxx_xxx
 */
export async function reqUpdateShoutHbData(danmu) {
  return await request.post('/qianDaoYu/updateHanHbJson', {
    han_hb_json: danmu,
    splid: store.state.liveId,
  });
}


/**
 * 生成数字
 */
export const reqGenerateNumber = (rangeList) => {
  const listStr = JSON.stringify(rangeList);
  return request.post('/neiDing/newShengChengNumber2', {
    splid: store.state.liveId,
    arrayStr: listStr
  });
}