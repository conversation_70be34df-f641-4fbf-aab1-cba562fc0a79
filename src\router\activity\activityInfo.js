export default [
  {
    path: 'themeSetting',
    name: 'themeSetting',
    component: () => import('@/views/activityViews/activityInfo/themeSetting'),
    meta: {
      index: '1',
      title: '主题设置',
      icon: 'el-icon-joymew-shouye1',
      parentIndex: '1',
    },
  },
  {
    path: 'activityEdit',
    name: 'activityEdit',
    component: () => import('@/views/activityViews/activityInfo/activityEdit/index'),
    meta: {
      index: '2',
      title: '信息修改',
      icon: 'el-icon-joymew-bianji',
      parentIndex: '1',
    },
  },
  {
    path: 'companyLogo',
    name: 'companyLogo',
    component: () => import('@/views/activityViews/activityInfo/companyLogo'),
    meta: {
      index: '3',
      title: 'LOGO设置',
      icon: 'el-icon-joymew-shezhi',
      parentIndex: '1',
    },
  },
  {
    path: 'customWish',
    name: 'customWish',
    component: () => import('@/views/activityViews/activityInfo/customWish'),
    meta: {
      index: '4',
      title: '自定义签到语',
      icon: 'el-icon-joymew-zidingyi',
      parentIndex: '1',
    },
  },
  {
    path: 'getPhone',
    name: 'getPhone',
    component: () => import('@/views/activityViews/activityInfo/getPhone'),
    meta: {
      index: '5',
      title: '获取手机号',
      icon: 'el-icon-joymew-shoujihao',
      parentIndex: '1',
    },
  },
  {
    path: 'curtainCall',
    name: 'curtainCall',
    component: () => import('@/views/activityViews/activityInfo/curtainCall'),
    meta: {
      index: '6',
      title: '谢幕设置',
      icon: 'el-icon-joymew-cunchufangshijieshu',
      parentIndex: '1',
    },
  },
  {
    path: 'livePhoto',
    name: 'livePhoto',
    component: () => import('@/views/activityViews/activityInfo/livePhoto'),
    meta: {
      index: '7',
      title: '现场照片',
      icon: 'el-icon-joymew-zhaopian',
      parentIndex: '1',
    },
  },
  {
    path: 'qrcodeFixed',
    name: 'qrcodeFixed',
    component: () => import('@/views/activityViews/activityInfo/qrcodeFixed'),
    meta: {
      index: '8',
      title: '固定二维码',
      icon: 'el-icon-joymew-erweima',
      parentIndex: '1',
    },
  },
  {
    path: 'hbkdRecharge',
    name: 'hbkdRecharge',
    component: () => import('@/views/activityViews/activityInfo/hbkdRecharge'),
    meta: {
      index: '9',
      title: '红包口袋充值',
      icon: 'el-icon-joymew-wodehongbao',
      parentIndex: '1',
    },
  },
  {
    path: 'custom3DSign',
    name: 'custom3DSign',
    component: () => import('@/views/activityViews/activityInfo/custom3DSign'),
    meta: {
      index: '10',
      title: '自定义3D签到',
      icon: 'el-icon-joymew-qiandao',
      parentIndex: '1',
    },
  },
  {
    path: 'launchCeremony',
    name: 'launchCeremony',
    component: () => import('@/views/activityViews/activityInfo/launchCeremony'),
    meta: {
      index: '12',
      title: '启动仪式',
      icon: 'el-icon-joymew-4wed_arch',
      parentIndex: '1',
    },
  },
  {
    path: 'seat',
    name: 'seat',
    component: () => import('@/views/activityViews/activityInfo/seat'),
    meta: {
      index: '13',
      title: '席位表',
      icon: 'el-icon-joymew-24gl-table',
      parentIndex: '1',
    },
  },
  {
    path: 'songChoose',
    name: 'songChoose',
    component: () => import('@/views/activityViews/activityInfo/songChoose'),
    meta: {
      index: '14',
      title: '点歌设置',
      icon: 'el-icon-joymew-dianyingzhiye-gequbang',
    },
  },
  {
    path: 'locationLimit',
    name: 'locationLimit',
    component: () => import('@/views/activityViews/activityInfo/locationLimit'),
    meta: {
      index: '15',
      title: '地理位置限制',
      icon: 'el-icon-joymew-diliweizhi',
      parentIndex: '1',
    },
  },
  {
    path: 'messageVerify',
    name: 'messageVerify',
    component: () => import('@/views/activityViews/activityInfo/messageVerify'),
    meta: {
      index: '16',
      title: '消息审核',
      icon: 'el-icon-joymew-shenhe',
      parentIndex: '1',
    },
  },
  {
    path: 'customMenu',
    name: 'customMenu',
    component: () => import('@/views/activityViews/activityInfo/customMenu'),
    meta: {
      index: '16',
      title: '大屏菜单设置',
      icon: 'el-icon-joymew-caidan',
      parentIndex: '1',
    },
  },
  {
    path: 'signWhiteList',
    name: 'signWhiteList',
    component: () => import('@/views/activityViews/activityInfo/signWhiteList'),
    meta: {
      index: '17',
      title: '签到白名单',
      icon: 'el-icon-joymew-baimingdan',
      parentIndex: '1',
    },
  },
  {
    path: 'configLink',
    name: 'ConfigLink',
    component: () => import('@/views/appTempViews/configLink'),
    meta: {
      index: '18',
      title: '链接生成',
      icon: 'el-icon-joymew-caidan',
      parentIndex: '1',
    },
  },
];
